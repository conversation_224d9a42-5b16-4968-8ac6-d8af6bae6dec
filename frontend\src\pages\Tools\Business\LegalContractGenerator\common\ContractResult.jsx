import React, { useState, useRef, useCallback } from 'react';
import {
  FiDownload, FiCopy, FiEdit3, FiR<PERSON>reshC<PERSON>, <PERSON>Check,
  FiFileText, FiShare2, FiPrinter, FiSave
} from 'react-icons/fi';
import { CONTRACT_TYPES } from '../utils/contractConstants';
import { exportContractToPDF, exportContractToWord } from '../utils/pdfExport';
import { generateContractPdfByType } from '../download';
import { useContractDirection } from '../hooks/useLanguageDirection';

const ContractResult = ({
  contract,
  contractType,
  formData,
  onStartOver,
  onBackToForm
}) => {
  const [copied, setCopied] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [exportFormat, setExportFormat] = useState('pdf');
  const contractRef = useRef(null);
  const contractTypeInfo = CONTRACT_TYPES[contractType];

  // Language direction support
  const { contractStyles, contractClasses } = useContractDirection(formData.language);



  // Copy contract to clipboard
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(contract);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy contract:', err);
    }
  };

  // Download contract in selected format
  const handleDownload = useCallback(async () => {
    setIsDownloading(true);
    try {
      let result;
      if (exportFormat === 'pdf') {
        // Use the new enhanced PDF generator with formatted content
        result = await generateContractPdfByType(
          formattedContract,
          contractType,
          formData,
          formData.language || 'English'
        );
      } else if (exportFormat === 'word') {
        result = await exportContractToWord(contract, contractType, formData, {
          filename: `${formData.contractTitle || contractTypeInfo.title}-${new Date().toISOString().split('T')[0]}.docx`
        });
      } else {
        // Plain text fallback
        const blob = new Blob([contract], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${formData.contractTitle || contractTypeInfo.title}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        result = { success: true, message: 'Contract downloaded as text file' };
      }

      if (result && !result.success) {
        console.error('Download failed:', result.error);
        // Could show a toast notification here
      }
    } catch (err) {
      console.error('Failed to download contract:', err);
    } finally {
      setIsDownloading(false);
    }
  }, [contract, contractType, formData, exportFormat, contractTypeInfo.title]);

  // Print contract
  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>${formData.contractTitle || contractTypeInfo.title}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
            h1, h2, h3 { color: #333; }
            .contract-content { white-space: pre-wrap; }
          </style>
        </head>
        <body>
          <div class="contract-content">${contract}</div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <div className="w-full max-w-6xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-8">
        <div className={`inline-flex items-center justify-center p-4 ${contractTypeInfo.bgColor} ${contractTypeInfo.borderColor} border rounded-full mb-6 backdrop-blur-sm`}>
          <FiCheck className="w-8 h-8 text-green-400 mr-3" />
          <span className="text-green-400 font-semibold text-lg">
            Contract Generated Successfully
          </span>
        </div>
        <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-green-200 to-blue-200 bg-clip-text text-transparent mb-4">
          Your {contractTypeInfo.title} is Ready
        </h2>
        <p className="text-slate-400 text-lg max-w-2xl mx-auto leading-relaxed">
          Your professional legal contract has been generated with AI-powered clauses and compliance checks. 
          Review, download, or make adjustments as needed.
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap justify-center gap-4 mb-8">
        {/* Enhanced PDF Download Button - Primary Action */}
        <button
          onClick={handleDownload}
          disabled={isDownloading || exportFormat !== 'pdf'}
          className="flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-500 hover:to-blue-500 disabled:from-slate-600 disabled:to-slate-700 text-white rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-sky-500/25 disabled:transform-none disabled:shadow-none"
        >
          {isDownloading ? (
            <FiRefreshCw className="w-6 h-6 animate-spin" />
          ) : (
            <FiDownload className="w-6 h-6" />
          )}
          {isDownloading ? 'Generating PDF...' : 'Download PDF'}
        </button>

        {/* Export Format Selector */}
        <div className="flex items-center gap-2 bg-slate-800/50 rounded-xl p-1 border border-slate-700/50">
          <button
            onClick={() => setExportFormat('pdf')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
              exportFormat === 'pdf'
                ? 'bg-sky-600 text-white shadow-lg'
                : 'text-slate-400 hover:text-white hover:bg-slate-700'
            }`}
          >
            PDF
          </button>
          <button
            onClick={() => setExportFormat('word')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
              exportFormat === 'word'
                ? 'bg-sky-600 text-white shadow-lg'
                : 'text-slate-400 hover:text-white hover:bg-slate-700'
            }`}
          >
            Word
          </button>
          <button
            onClick={() => setExportFormat('text')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
              exportFormat === 'text'
                ? 'bg-sky-600 text-white shadow-lg'
                : 'text-slate-400 hover:text-white hover:bg-slate-700'
            }`}
          >
            Text
          </button>
        </div>

        {/* Secondary Download Button for Other Formats */}
        {exportFormat !== 'pdf' && (
          <button
            onClick={handleDownload}
            disabled={isDownloading}
            className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-purple-500/25"
          >
            {isDownloading ? (
              <FiRefreshCw className="w-5 h-5 animate-spin" />
            ) : (
              <FiDownload className="w-5 h-5" />
            )}
            {isDownloading ? 'Preparing...' : `Download ${exportFormat.toUpperCase()}`}
          </button>
        )}

        <button
          onClick={handleCopy}
          className="flex items-center gap-2 px-6 py-3 bg-slate-700/80 hover:bg-slate-600 text-white rounded-xl font-semibold transition-all duration-300 border border-slate-600/50 hover:border-slate-500"
        >
          {copied ? (
            <>
              <FiCheck className="w-5 h-5 text-green-400" />
              Copied!
            </>
          ) : (
            <>
              <FiCopy className="w-5 h-5" />
              Copy Text
            </>
          )}
        </button>

        <button
          onClick={handlePrint}
          className="flex items-center gap-2 px-6 py-3 bg-slate-700/80 hover:bg-slate-600 text-white rounded-xl font-semibold transition-all duration-300 border border-slate-600/50 hover:border-slate-500"
        >
          <FiPrinter className="w-5 h-5" />
          Print
        </button>

        <button
          onClick={onBackToForm}
          className="flex items-center gap-2 px-6 py-3 bg-slate-700/80 hover:bg-slate-600 text-white rounded-xl font-semibold transition-all duration-300 border border-slate-600/50 hover:border-slate-500"
        >
          <FiEdit3 className="w-5 h-5" />
          Edit Contract
        </button>
      </div>

      {/* Contract Display */}
      <div className="bg-gradient-to-br from-slate-900/50 to-slate-800/30 backdrop-blur-sm rounded-3xl border border-slate-700/50 p-6 md:p-8 shadow-2xl shadow-sky-500/5 mb-8">
        {/* Contract Header */}
        <div className="flex items-center justify-between mb-8 pb-6 border-b border-slate-700/50">
          <div className="flex items-center gap-4">
            <div className={`p-3 ${contractTypeInfo.bgColor} ${contractTypeInfo.borderColor} border rounded-xl backdrop-blur-sm`}>
              <contractTypeInfo.icon className={`w-8 h-8 ${contractTypeInfo.textColor}`} />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-white mb-1">
                {formData.contractTitle || contractTypeInfo.title}
              </h3>
              <p className="text-slate-400 text-sm">
                Generated on {new Date().toLocaleDateString()} • {formData.jurisdiction || 'General Jurisdiction'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 px-4 py-2 bg-slate-800/50 rounded-lg border border-slate-600/30">
            <FiFileText className="w-5 h-5 text-slate-400" />
            <span className="text-slate-300 text-sm font-medium">
              Legal Contract
            </span>
          </div>
        </div>

        {/* Enhanced Contract Content with Professional Typography */}
        <div
          ref={contractRef}
          className={`${contractClasses.content} prose prose-invert max-w-none`}
          style={{
            ...contractStyles.content
          }}
        >
          <div className="text-slate-300 leading-relaxed whitespace-pre-wrap">
            {contract}
          </div>
        </div>
      </div>

      {/* Action Footer */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 p-6 bg-slate-800/50 border border-slate-700 rounded-xl">
        <div className="text-center sm:text-left">
          <h4 className="text-white font-semibold mb-1">What's Next?</h4>
          <p className="text-slate-400 text-sm">
            Review your contract carefully and consider having it reviewed by a legal professional before signing.
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={onStartOver}
            className="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white rounded-xl font-medium transition-all duration-300 flex items-center gap-2"
          >
            <FiRefreshCw className="w-4 h-4" />
            Create New Contract
          </button>
        </div>
      </div>

      {/* Legal Disclaimer */}
      <div className="mt-8 p-6 bg-yellow-500/10 border border-yellow-500/30 rounded-xl">
        <div className="flex items-start gap-3">
          <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
          <div>
            <h4 className="text-yellow-400 font-semibold mb-2">Important Legal Notice</h4>
            <p className="text-slate-400 text-sm leading-relaxed">
              This contract was generated using AI technology and should be reviewed by a qualified attorney 
              before execution. While our AI incorporates legal best practices, every situation is unique and 
              may require specific legal considerations. Dosky is not responsible for the legal validity or 
              enforceability of generated contracts.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractResult;
