// Legal Contract AI Prompts for different contract types

/**
 * Build the main prompt for legal contract generation
 */
export const buildLegalContractPrompt = (contractData) => {
    const { contractType, language, jurisdiction } = contractData;

    const basePrompt = getBaseContractPrompt(language, jurisdiction);
    const specificPrompt = getContractSpecificPrompt(contractType, contractData);

    return `${basePrompt}\n\n${specificPrompt}`;
};

/**
 * Base prompt for all contract types
 */
const getBaseContractPrompt = (language, jurisdiction) => {
    return `You are an expert legal contract drafting AI with extensive knowledge of contract law and legal best practices.
Your task is to generate a comprehensive, legally sound contract that follows professional standards and includes all necessary clauses.

**JURISDICTION:** ${jurisdiction}
**LANGUAGE:** ${language}

Generate a professional, comprehensive contract that includes:

IMPORTANT INSTRUCTIONS:
- Generate a professional, legally sound contract in ${language}
- Ensure compliance with ${jurisdiction} legal standards and requirements
- Use clear, precise legal language appropriate for the jurisdiction
- Include all necessary legal clauses and protections
- Structure the contract with proper headings, sections, and numbering
- Ensure the contract is comprehensive yet readable
- Include appropriate legal disclaimers and standard clauses
- Use professional formatting with clear section breaks
- Generate plain text without any special formatting tags

LEGAL STANDARDS:
- Follow ${jurisdiction} contract law requirements
- Include proper consideration clauses
- Ensure mutual obligations are clearly defined
- Include appropriate dispute resolution mechanisms
- Add standard legal protections (force majeure, severability, etc.)
- Use jurisdiction-appropriate legal terminology`;
};

/**
 * Contract-specific prompts for different contract types
 */
const getContractSpecificPrompt = (contractType, contractData) => {
    if (contractType === 'service') {
        return generateServiceAgreementPrompt(contractData);
    } else if (contractType === 'partnership') {
        return generatePartnershipAgreementPrompt(contractData);
    }

    throw new Error('Unsupported contract type');
};

/**
 * Service Agreement specific prompt
 */
const generateServiceAgreementPrompt = (data) => {
    return `CONTRACT TYPE: SERVICE AGREEMENT

Generate a comprehensive Service Agreement with the following details:

PARTIES:
- Service Provider: ${data.partyOneName}
- Client: ${data.partyTwoName}
- Effective Date: ${data.effectiveDate}

CONTRACT DETAILS:
- Title: ${data.contractTitle || 'Service Agreement'}
- Service Provider Email: ${data.partyOneEmail || 'Not provided'}
- Service Provider Address: ${data.partyOneAddress || 'Not provided'}
- Client Email: ${data.partyTwoEmail || 'Not provided'}
- Client Address: ${data.partyTwoAddress || 'Not provided'}

SCOPE OF WORK:
${data.scopeOfWork || 'To be defined'}

DELIVERABLES:
${data.deliverables || 'To be specified'}

PROJECT TIMELINE:
${data.timeline || 'To be established'}

PAYMENT TERMS:
- Total Amount: $${data.paymentAmount || 'TBD'}
- Payment Schedule: ${data.paymentSchedule || 'To be agreed'}
- Payment Terms: ${data.paymentTerms || 'Standard terms apply'}

ADDITIONAL TERMS:
- Change Request Policy: ${data.changeRequestPolicy || 'Standard change management applies'}
- Cancellation Policy: ${data.cancellationPolicy || 'Standard cancellation terms apply'}
- Intellectual Property: ${data.intellectualProperty || 'Standard IP terms apply'}
- Termination Clause: ${data.terminationClause || 'Standard termination terms apply'}
- Warranties: ${data.warranties || 'Standard warranties apply'}
- Liability Limitation: ${data.liabilityLimitation || 'Standard liability limitations apply'}

OPTIONAL CLAUSES:
- Include Confidentiality: ${data.includeConfidentiality ? 'Yes' : 'No'}
- Include Indemnification: ${data.includeIndemnification ? 'Yes' : 'No'}
- Include Force Majeure: ${data.includeForcemajeure ? 'Yes' : 'No'}

Additional Terms: ${data.additionalTerms || 'None specified'}

REQUIRED SECTIONS:
1. Parties and Recitals
2. Scope of Work and Services
3. Deliverables and Timeline
4. Payment Terms and Conditions
5. Intellectual Property Rights
6. Confidentiality (if requested)
7. Termination and Cancellation
8. Warranties and Representations
9. Limitation of Liability
10. Indemnification (if requested)
11. Force Majeure (if requested)
12. General Provisions (Governing Law, Severability, Entire Agreement, etc.)
13. Signature Blocks`;
};

/**
 * Partnership Agreement specific prompt
 */
const generatePartnershipAgreementPrompt = (data) => {
    return `CONTRACT TYPE: PARTNERSHIP AGREEMENT

Generate a comprehensive Partnership Agreement with the following details:

PARTIES:
- Partner 1: ${data.partyOneName}
- Partner 2: ${data.partyTwoName}
- Effective Date: ${data.effectiveDate}

CONTRACT DETAILS:
- Partnership Name: ${data.partnershipName || 'To be determined'}
- Business Purpose: ${data.businessPurpose || 'General business partnership'}
- Partner 1 Email: ${data.partyOneEmail || 'Not provided'}
- Partner 1 Address: ${data.partyOneAddress || 'Not provided'}
- Partner 2 Email: ${data.partyTwoEmail || 'Not provided'}
- Partner 2 Address: ${data.partyTwoAddress || 'Not provided'}

CAPITAL CONTRIBUTIONS:
- Partner 1 Contribution: $${data.partnerOneContribution || 'TBD'}
- Partner 2 Contribution: $${data.partnerTwoContribution || 'TBD'}
- Contribution Type: ${data.contributionType || 'Cash and/or assets'}
- Additional Contributions: ${data.additionalContributions || 'As agreed by partners'}

PROFIT AND LOSS SHARING:
- Partner 1 Profit Share: ${data.partnerOneProfitShare || '50'}%
- Partner 2 Profit Share: ${data.partnerTwoProfitShare || '50'}%
- Loss Distribution: ${data.lossDistribution || 'Same as profit sharing'}
- Distribution Schedule: ${data.distributionSchedule || 'Quarterly'}

MANAGEMENT AND DECISION MAKING:
- Management Structure: ${data.managementStructure || 'Equal management rights'}
- Decision Making Authority: ${data.decisionMakingAuthority || 'Unanimous consent for major decisions'}
- Voting Rights: ${data.votingRights || 'Equal voting rights'}
- Daily Operations: ${data.dailyOperations || 'Both partners authorized'}

PARTNERSHIP TERMS:
- Partnership Duration: ${data.partnershipDuration || 'Indefinite'}
- Termination Conditions: ${data.terminationConditions || 'Standard termination clauses'}
- Withdrawal Process: ${data.withdrawalProcess || 'Standard withdrawal procedures'}
- Buy-Sell Agreement: ${data.buySellAgreement || 'Standard buy-sell provisions'}

DISPUTE RESOLUTION:
- Dispute Resolution Method: ${data.disputeResolution || 'Mediation followed by arbitration'}
- Governing Law: ${data.governingLaw || 'State/Country law'}
- Arbitration Rules: ${data.arbitrationRules || 'Standard arbitration procedures'}

ADDITIONAL TERMS:
- Non-Compete Clause: ${data.nonCompeteClause || 'Standard non-compete provisions'}
- Confidentiality: ${data.confidentialityTerms || 'Standard confidentiality terms'}
- Books and Records: ${data.booksAndRecords || 'Standard record-keeping requirements'}
- Banking and Finance: ${data.bankingFinance || 'Standard banking arrangements'}

OPTIONAL CLAUSES:
- Include Death/Disability: ${data.includeDeathDisability ? 'Yes' : 'No'}
- Include Non-Compete: ${data.includeNonCompete ? 'Yes' : 'No'}
- Include Buy-Sell: ${data.includeBuySell ? 'Yes' : 'No'}

Additional Terms: ${data.additionalTerms || 'None specified'}

REQUIRED SECTIONS:
1. Partnership Formation and Purpose
2. Capital Contributions and Ownership
3. Profit and Loss Distribution
4. Management and Decision Making Authority
5. Partner Rights and Responsibilities
6. Partnership Duration and Termination
7. Withdrawal and Buy-Sell Provisions
8. Dispute Resolution Mechanisms
9. Death and Disability Provisions (if requested)
10. Non-Compete and Confidentiality (if requested)
11. Books, Records, and Financial Management
12. General Provisions (Governing Law, Severability, Entire Agreement, etc.)
13. Signature Blocks`;
};


