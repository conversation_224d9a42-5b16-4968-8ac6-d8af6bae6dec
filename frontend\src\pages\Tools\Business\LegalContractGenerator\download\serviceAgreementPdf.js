// Service Agreement PDF Generator
// Specialized PDF generation for Service Agreements

import { generateContractPdf } from './contractPdfGenerator';

/**
 * Generate PDF specifically for Service Agreements
 * @param {string} contractContent - The service agreement content
 * @param {Object} formData - Form data containing service agreement details
 * @param {string} language - Language for RTL/LTR support
 */
export const generateServiceAgreementPdf = async (contractContent, formData, language = 'English') => {
    return await generateContractPdf(contractContent, 'service', formData, language);
};



/**
 * Service Agreement specific styling options
 */
export const serviceAgreementPdfOptions = {
    title: 'Service Agreement',
    primaryColor: '#1e40af', // Professional blue
    secondaryColor: '#059669', // Success green
    accentColor: '#d97706', // Warning amber
    fontFamily: 'Georgia',
    fontSize: {
        title: 20,
        heading: 16,
        subheading: 14,
        body: 11,
        small: 9
    },
    margins: {
        top: 20,
        bottom: 20,
        left: 15,
        right: 15
    },
    spacing: {
        afterTitle: 15,
        afterHeading: 12,
        afterSubheading: 10,
        afterParagraph: 8,
        afterListItem: 6
    }
};
