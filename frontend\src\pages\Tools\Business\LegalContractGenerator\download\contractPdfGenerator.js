// Legal Contract PDF Generator
// Based on Business Plan Generator patterns for consistent formatting

import jsPDF from 'jspdf';
import { loadFonts } from '../../components/utils/fontLoader';
import { PAGE_MARGIN, FONT_SIZES, COLORS } from '../../components/utils/pdfStyles';

/**
 * Generate PDF for legal contracts with professional formatting
 * @param {string} contractContent - The contract content as plain text
 * @param {string} contractType - Type of contract (service, partnership)
 * @param {Object} formData - Form data containing contract details
 * @param {string} language - Language for RTL/LTR support
 */
export const generateContractPdf = async (contractContent, contractType, formData, language = 'English') => {
    const doc = new jsPDF();
    const isRtl = ['Arabic', 'Hebrew', 'Persian', 'Urdu', 'Yiddish'].includes(language);
    doc.internal.isRtl = isRtl;

    const fontName = await loadFonts(doc, isRtl);
    doc.setFont(fontName);
    let yPos = PAGE_MARGIN;

    // Add contract header
    yPos = addContractHeader(doc, contractType, formData, yPos, isRtl);

    // Process contract content as plain text
    const lines = contractContent.split('\n');

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        // Check if we need a new page
        if (yPos > doc.internal.pageSize.getHeight() - 30) {
            doc.addPage();
            yPos = PAGE_MARGIN;
        }

        // Render as plain text paragraph
        yPos = renderContractParagraph(doc, line, yPos, isRtl);
    }

    // Add footer
    addContractFooter(doc, formData, isRtl);

    // Generate filename
    const contractTitle = formData.contractTitle || getContractTitle(contractType);
    const filename = `${contractTitle.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
    
    doc.save(filename);
    return { success: true, filename };
};

/**
 * Add contract header with title and metadata
 */
const addContractHeader = (doc, contractType, formData, yPos, isRtl) => {
    const pageWidth = doc.internal.pageSize.getWidth();
    const x = isRtl ? pageWidth - PAGE_MARGIN : PAGE_MARGIN;
    const textOptions = { align: isRtl ? 'right' : 'left' };

    // Contract title
    doc.setFontSize(FONT_SIZES.H1);
    doc.setTextColor(COLORS.PRIMARY);
    doc.setFont(doc.getFont().fontName, 'bold');
    
    const title = formData.contractTitle || getContractTitle(contractType);
    doc.text(title, x, yPos, textOptions);
    yPos += 15;

    // Add underline
    const titleWidth = doc.getTextWidth(title);
    if (isRtl) {
        doc.line(pageWidth - PAGE_MARGIN - titleWidth, yPos - 2, pageWidth - PAGE_MARGIN, yPos - 2);
    } else {
        doc.line(PAGE_MARGIN, yPos - 2, PAGE_MARGIN + titleWidth, yPos - 2);
    }
    yPos += 10;

    // Contract metadata
    doc.setFontSize(FONT_SIZES.SMALL);
    doc.setTextColor(COLORS.GRAY);
    doc.setFont(doc.getFont().fontName, 'normal');
    
    const metadata = [
        `Generated on: ${new Date().toLocaleDateString()}`,
        `Contract Type: ${getContractTitle(contractType)}`,
        formData.jurisdiction && `Jurisdiction: ${formData.jurisdiction}`,
        formData.effectiveDate && `Effective Date: ${formData.effectiveDate}`
    ].filter(Boolean);

    metadata.forEach(item => {
        doc.text(item, x, yPos, textOptions);
        yPos += 6;
    });

    return yPos + 15;
};





/**
 * Render contract paragraphs
 */
const renderContractParagraph = (doc, text, yPos, isRtl) => {
    const pageWidth = doc.internal.pageSize.getWidth();
    const x = isRtl ? pageWidth - PAGE_MARGIN : PAGE_MARGIN;
    const textOptions = { align: isRtl ? 'right' : 'left' };

    doc.setFontSize(FONT_SIZES.P);
    doc.setTextColor(COLORS.TEXT);
    doc.setFont(doc.getFont().fontName, 'normal');
    
    const splitText = doc.splitTextToSize(text, pageWidth - (PAGE_MARGIN * 2));
    doc.text(splitText, x, yPos, textOptions);
    
    const textHeight = doc.getTextDimensions(splitText).h;
    return yPos + textHeight + 8;
};



/**
 * Add contract footer
 */
const addContractFooter = (doc, formData, isRtl) => {
    const pageHeight = doc.internal.pageSize.getHeight();
    const pageWidth = doc.internal.pageSize.getWidth();
    const footerY = pageHeight - 20;
    
    doc.setFontSize(FONT_SIZES.SMALL);
    doc.setTextColor(COLORS.GRAY);
    doc.setFont(doc.getFont().fontName, 'normal');
    
    // Page number
    const pageNum = `Page ${doc.internal.getNumberOfPages()}`;
    if (isRtl) {
        doc.text(pageNum, PAGE_MARGIN, footerY, { align: 'left' });
    } else {
        doc.text(pageNum, pageWidth - PAGE_MARGIN, footerY, { align: 'right' });
    }
    
    // Generated by text
    const generatedText = 'Generated by Dosky Legal Contract Generator';
    if (isRtl) {
        doc.text(generatedText, pageWidth - PAGE_MARGIN, footerY, { align: 'right' });
    } else {
        doc.text(generatedText, PAGE_MARGIN, footerY, { align: 'left' });
    }
};

/**
 * Get contract title based on type
 */
const getContractTitle = (contractType) => {
    const titles = {
        service: 'Service Agreement',
        partnership: 'Partnership Agreement'
    };
    return titles[contractType] || 'Legal Contract';
};
