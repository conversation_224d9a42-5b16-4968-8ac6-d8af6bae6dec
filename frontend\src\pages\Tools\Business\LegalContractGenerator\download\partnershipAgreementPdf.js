// Partnership Agreement PDF Generator
// Specialized PDF generation for Partnership Agreements

import { generateContractPdf } from './contractPdfGenerator';

/**
 * Generate PDF specifically for Partnership Agreements
 * @param {string} contractContent - The partnership agreement content
 * @param {Object} formData - Form data containing partnership agreement details
 * @param {string} language - Language for RTL/LTR support
 */
export const generatePartnershipAgreementPdf = async (contractContent, formData, language = 'English') => {
    return await generateContractPdf(contractContent, 'partnership', formData, language);
};



/**
 * Partnership Agreement specific styling options
 */
export const partnershipAgreementPdfOptions = {
    title: 'Partnership Agreement',
    primaryColor: '#7c3aed', // Purple for partnership
    secondaryColor: '#059669', // Success green
    accentColor: '#dc2626', // Red for important clauses
    fontFamily: 'Georgia',
    fontSize: {
        title: 20,
        heading: 16,
        subheading: 14,
        body: 11,
        small: 9
    },
    margins: {
        top: 20,
        bottom: 20,
        left: 15,
        right: 15
    },
    spacing: {
        afterTitle: 15,
        afterHeading: 12,
        afterSubheading: 10,
        afterParagraph: 8,
        afterListItem: 6
    }
};
